export interface Subject {
  id: string;
  name: string;
  color: string;
  createdAt: Date;
}

export interface TimerSession {
  id: string;
  startTime: Date;
  endTime?: Date | null;
  duration: number; // in seconds
  subject?: string;
  subjectColor?: string;
  mode: 'stopwatch' | 'pomodoro';
  phase?: 'work' | 'break';
  completed: boolean;
  notes?: string;
  taskName?: string;
  taskType?: string;
  productivityRating?: number;
  feedback?: string;
  date: string; // YYYY-MM-DD format
  // Legacy fields for backward compatibility
  subjectId?: string;
  isBreak?: boolean;
}

export interface Goal {
  id: string;
  title: string;
  description: string;
  targetDate: Date;
  subjectId?: string;
  priority: 'low' | 'medium' | 'high';
  completed: boolean;
  createdAt: Date;
}

// Enhanced Task Management Types
export interface Task {
  id: string;
  user_id: string;
  title: string;
  description?: string;
  category: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'todo' | 'in_progress' | 'completed' | 'cancelled';
  due_date?: Date;
  start_date?: Date;
  completion_date?: Date;
  estimated_duration?: number; // in minutes
  actual_duration?: number; // in minutes
  progress_percentage: number;
  subject_id?: string;
  tags: string[];
  is_milestone: boolean;
  parent_task_id?: string;
  order_index: number;
  reminder_enabled: boolean;
  reminder_time?: Date;
  study_session_ids: string[];
  total_study_time: number; // in minutes
  notes?: string;
  attachments: any[];
  metadata: any;
  created_at: Date;
  updated_at: Date;
}

export interface TaskCategory {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  color: string;
  icon: string;
  order_index: number;
  is_default: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface TaskMilestone {
  id: string;
  task_id: string;
  title: string;
  description?: string;
  target_percentage: number;
  achieved_at?: Date;
  is_achieved: boolean;
  reward_points: number;
  created_at: Date;
}

export interface TaskFilters {
  status?: string[];
  category?: string[];
  priority?: string[];
  due_date_from?: Date;
  due_date_to?: Date;
  search?: string;
  tags?: string[];
  is_milestone?: boolean;
  parent_task_id?: string;
}

export interface TaskStats {
  total_tasks: number;
  completed_tasks: number;
  in_progress_tasks: number;
  overdue_tasks: number;
  completion_rate: number;
  total_study_time: number;
  average_completion_time: number;
  tasks_by_category: { [key: string]: number };
  tasks_by_priority: { [key: string]: number };
}

export interface PomodoroSettings {
  workDuration: number; // in minutes
  shortBreakDuration: number; // in minutes
  longBreakDuration: number; // in minutes
  sessionsUntilLongBreak: number;
}