import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { ChartBar as BarChart3, Calendar, Clock, Target, TrendingUp, Activity } from 'lucide-react-native';
import IsotopeLogo from '@/components/IsotopeLogo';
import { useTimer } from '@/hooks/useTimer';
import { useSubjects } from '@/hooks/useSubjects';
import { useTheme } from '@/contexts/ThemeContext';
import { useThemedStyles } from '@/hooks/useThemedStyles';
import { Theme } from '@/types/theme';

const { width } = Dimensions.get('window');

export default function AnalyticsScreen() {
  const { theme } = useTheme();
  const style = useThemedStyles(themedStyles);
  const { sessions, getTotalTimeToday } = useTimer();
  const { subjects, getSubjectById } = useSubjects();
  const [selectedPeriod, setSelectedPeriod] = useState('week');

  const periods = ['week', 'month', 'year'];

  const getSessionsForPeriod = () => {
    const now = new Date();
    const startDate = new Date();
    
    switch (selectedPeriod) {
      case 'week':
        startDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(now.getMonth() - 1);
        break;
      case 'year':
        startDate.setFullYear(now.getFullYear() - 1);
        break;
    }
    
    return sessions.filter(session => session.startTime >= startDate);
  };

  const getSubjectStats = () => {
    const periodSessions = getSessionsForPeriod();
    const subjectStats: { [key: string]: { name: string; time: number; color: string; sessions: number } } = {};
    
    periodSessions.forEach(session => {
      const subjectId = session.subjectId || 'none';
      const subject = subjectId !== 'none' ? getSubjectById(subjectId) : null;
      const name = subject?.name || 'No Subject';
      const color = subject?.color || '#9CA3AF';
      
      if (!subjectStats[subjectId]) {
        subjectStats[subjectId] = { name, time: 0, color, sessions: 0 };
      }
      
      subjectStats[subjectId].time += session.duration;
      subjectStats[subjectId].sessions += 1;
    });
    
    return Object.values(subjectStats).sort((a, b) => b.time - a.time);
  };

  const getDailyStats = () => {
    const dailyStats: { [key: string]: number } = {};
    const periodSessions = getSessionsForPeriod();
    
    periodSessions.forEach(session => {
      const dateKey = session.startTime.toDateString();
      if (!dailyStats[dateKey]) {
        dailyStats[dateKey] = 0;
      }
      dailyStats[dateKey] += session.duration;
    });
    
    return dailyStats;
  };

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  const formatTimeShort = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h`;
    }
    return `${minutes}m`;
  };

  const subjectStats = getSubjectStats();
  const dailyStats = getDailyStats();
  const totalTime = getSessionsForPeriod().reduce((sum, session) => sum + session.duration, 0);
  const totalSessions = getSessionsForPeriod().length;
  const averageSession = totalSessions > 0 ? totalTime / totalSessions : 0;

  const renderBarChart = () => {
    const last7Days = Array.from({ length: 7 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - (6 - i));
      return date;
    });

    const maxTime = Math.max(...last7Days.map(date => dailyStats[date.toDateString()] || 0));

    return (
      <View style={style.chartContainer}>
        <Text style={style.chartTitle}>Daily Study Time</Text>
        <View style={style.barChart}>
          {last7Days.map((date, index) => {
            const time = dailyStats[date.toDateString()] || 0;
            const height = maxTime > 0 ? (time / maxTime) * 120 : 0;
            
            return (
              <View key={index} style={style.barContainer}>
                <View style={style.barWrapper}>
                  <LinearGradient
                    colors={theme.colors.gradients.primary as readonly [string, string]}
                    style={[style.bar, { height: Math.max(height, 4) }]}
                  />
                </View>
                <Text style={style.barLabel}>
                  {date.toLocaleDateString('en-US', { weekday: 'short' })}
                </Text>
                <Text style={style.barValue}>{formatTimeShort(time)}</Text>
              </View>
            );
          })}
        </View>
      </View>
    );
  };

  const renderSubjectChart = () => {
    const totalSubjectTime = subjectStats.reduce((sum, stat) => sum + stat.time, 0);

    return (
      <View style={style.chartContainer}>
        <Text style={style.chartTitle}>Subject Distribution</Text>
        {totalSubjectTime > 0 ? (
          <>
            <View style={style.pieChart}>
              {subjectStats.map((stat, index) => {
                const percentage = (stat.time / totalSubjectTime) * 100;
                return (
                  <View
                    key={index}
                    style={[
                      style.pieSlice,
                      {
                        backgroundColor: stat.color,
                        width: `${percentage}%`,
                      },
                    ]}
                  />
                );
              })}
            </View>
            <View style={style.pieLegend}>
              {subjectStats.map((stat, index) => (
                <View key={index} style={style.legendItem}>
                  <View style={[style.legendColor, { backgroundColor: stat.color }]} />
                  <Text style={style.legendText}>{stat.name}</Text>
                  <Text style={style.legendValue}>{formatTime(stat.time)}</Text>
                </View>
              ))}
            </View>
          </>
        ) : (
          <View style={style.emptyChart}>
            <BarChart3 size={48} color={theme.colors.text.secondary} />
            <Text style={style.emptyText}>No data available</Text>
          </View>
        )}
      </View>
    );
  };

    return (
      <ScrollView style={style.container} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <LinearGradient
          colors={[theme.colors.background.primary, theme.colors.background.secondary]}
          style={style.headerGradient}
        >
          <View style={style.header}>
            <View>
              <IsotopeLogo size="medium" />
              <Text style={style.subtitle}>Track your study patterns</Text>
            </View>
          </View>
        </LinearGradient>

        {/* Period Selector */}
        <View style={style.periodSection}>
          <View style={style.periodSelector}>
            {periods.map((period) => (
              <TouchableOpacity
                key={period}
                style={[
                  style.periodButton,
                  selectedPeriod === period && style.periodButtonActive,
                ]}
                onPress={() => setSelectedPeriod(period)}
              >
                <Text
                  style={[
                    style.periodText,
                    selectedPeriod === period && style.periodTextActive,
                  ]}
                >
                  {period.charAt(0).toUpperCase() + period.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Stats Overview */}
        <View style={style.statsSection}>
          <View style={style.statsGrid}>
            <View style={style.statCard}>
          <View style={style.statIcon}>
            <Clock size={20} color={theme.colors.accent.primary} />
          </View>
          <Text style={style.statValue}>{formatTime(totalTime)}</Text>
          <Text style={style.statLabel}>Total Time</Text>
        </View>

        <View style={style.statCard}>
          <View style={style.statIcon}>
            <Activity size={20} color={theme.colors.status.success} />
          </View>
          <Text style={style.statValue}>{totalSessions}</Text>
          <Text style={style.statLabel}>Sessions</Text>
        </View>

        <View style={style.statCard}>
          <View style={style.statIcon}>
            <TrendingUp size={20} color={theme.colors.status.warning} />
          </View>
          <Text style={style.statValue}>{formatTimeShort(averageSession)}</Text>
          <Text style={style.statLabel}>Avg Session</Text>
        </View>

        <View style={style.statCard}>
          <View style={style.statIcon}>
            <Target size={20} color={theme.colors.status.error} />
          </View>
          <Text style={style.statValue}>{formatTimeShort(getTotalTimeToday())}</Text>
          <Text style={style.statLabel}>Today</Text>
        </View>
      </View>
    </View>

    {/* Charts */}
    <View style={style.chartsSection}>
      {renderBarChart()}
      {renderSubjectChart()}
    </View>

    {/* Insights */}
    <View style={style.insightsSection}>
      <Text style={style.sectionTitle}>Insights</Text>

      {totalTime > 0 ? (
        <>
          <View style={style.insightCard}>
            <View style={style.insightHeader}>
              <Activity size={20} color={theme.colors.status.success} />
              <Text style={style.insightTitle}>Study Pattern</Text>
            </View>
            <Text style={style.insightText}>
              You've completed {totalSessions} study sessions this {selectedPeriod},
              with an average session length of {formatTime(averageSession)}.
            </Text>
          </View>

          {subjectStats.length > 0 && (
            <View style={style.insightCard}>
              <View style={style.insightHeader}>
                <Target size={20} color={theme.colors.accent.primary} />
                <Text style={style.insightTitle}>Top Subject</Text>
              </View>
              <Text style={style.insightText}>
                You've spent the most time on {subjectStats[0].name} with {formatTime(subjectStats[0].time)}
                across {subjectStats[0].sessions} sessions.
              </Text>
            </View>
          )}

          <View style={style.insightCard}>
            <View style={style.insightHeader}>
              <TrendingUp size={20} color={theme.colors.status.warning} />
              <Text style={style.insightTitle}>Progress</Text>
            </View>
            <Text style={style.insightText}>
              Keep up the great work! Consistent study sessions lead to better learning outcomes.
            </Text>
          </View>
        </>
      ) : (
        <View style={style.emptyInsights}>
          <BarChart3 size={48} color={theme.colors.text.secondary} />
          <Text style={style.emptyTitle}>No data yet</Text>
          <Text style={style.emptyText}>
            Start using the timer to see your study analytics here.
          </Text>
        </View>
      )}
    </View>
</ScrollView>
    );
  }

const themedStyles = (theme: Theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background.primary,
  },
  headerGradient: {
    paddingTop: 60,
    paddingBottom: 20,
  },
  header: {
    paddingHorizontal: 24,
  },
  subtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.text.secondary,
    marginTop: 4,
  },
  periodSection: {
    paddingHorizontal: 24,
    marginVertical: 16,
  },
  periodSelector: {
    flexDirection: 'row',
    backgroundColor: theme.colors.background.card,
    borderRadius: 12,
    padding: 4,
    shadowColor: theme.colors.ui.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  periodButton: {
    flex: 1,
    paddingVertical: 10,
    alignItems: 'center',
    borderRadius: 8,
  },
  periodButtonActive: {
    backgroundColor: theme.colors.accent.primary,
  },
  periodText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: theme.colors.text.secondary,
  },
  periodTextActive: {
    color: theme.colors.interactive.button.text,
  },
  statsSection: {
    paddingHorizontal: 24,
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  statCard: {
    flex: 1,
    minWidth: (width - 60) / 2,
    backgroundColor: theme.colors.background.card,
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    shadowColor: theme.colors.ui.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statIcon: {
    width: 40,
    height: 40,
    backgroundColor: theme.colors.background.tertiary, // Assuming tertiary background for icon
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  statValue: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: theme.colors.text.primary,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: theme.colors.text.secondary,
    textAlign: 'center',
  },
  chartsSection: {
    paddingHorizontal: 24,
  },
  chartContainer: {
    backgroundColor: theme.colors.background.card,
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: theme.colors.ui.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  chartTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text.primary,
    marginBottom: 16,
  },
  barChart: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    height: 160,
  },
  barContainer: {
    alignItems: 'center',
    flex: 1,
  },
  barWrapper: {
    height: 120,
    justifyContent: 'flex-end',
    marginBottom: 8,
  },
  bar: {
    width: 20,
    borderRadius: 10,
    minHeight: 4,
  },
  barLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: theme.colors.text.secondary,
    marginBottom: 2,
  },
  barValue: {
    fontSize: 10,
    fontFamily: 'Inter-Medium',
    color: theme.colors.text.primary,
  },
  pieChart: {
    flexDirection: 'row',
    width: '100%',
    height: 8,
    backgroundColor: theme.colors.ui.border,
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 16,
  },
  pieSlice: {
    height: '100%',
  },
  pieLegend: {
    gap: 8,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  legendText: {
    flex: 1,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.text.primary,
  },
  legendValue: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: theme.colors.text.secondary,
  },
  emptyChart: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.text.secondary,
    marginTop: 8,
  },
  insightsSection: {
    paddingHorizontal: 24,
    marginVertical: 16,
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text.primary,
    marginBottom: 16,
  },
  insightCard: {
    backgroundColor: theme.colors.background.card,
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    shadowColor: theme.colors.ui.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  insightHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  insightTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text.primary,
  },
  insightText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.text.secondary,
    lineHeight: 20,
  },
  emptyInsights: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text.secondary,
    marginTop: 16,
    marginBottom: 8,
  },
});
